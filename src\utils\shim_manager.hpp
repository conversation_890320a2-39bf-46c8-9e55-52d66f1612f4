#pragma once

#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <sstream>
#include "../core/config.hpp"
#include "output.hpp"

#ifdef _WIN32
#include <windows.h>
#include <shellapi.h>
#include <process.h>
#include <iostream>
#endif

namespace sco {

struct ShimInfo {
    std::string name;
    std::filesystem::path target_path;
    std::filesystem::path shim_path;
    std::vector<std::string> args;
    bool is_gui = false;
};

#ifdef _WIN32
#ifndef ERROR_ELEVATION_REQUIRED
#define ERROR_ELEVATION_REQUIRED 740
#endif
#define INFINITE_WAIT INFINITE
#endif

class ShimManager {
public:
    // Execute shim logic - equivalent to the original Scoop shim executable
    static int execute_shim(const std::filesystem::path& shim_path,
                           const std::vector<std::string>& command_line_args) {
#ifdef _WIN32
        // Get the .shim config file path
        auto config_path = shim_path;
        config_path.replace_extension(".shim");

        if (!std::filesystem::exists(config_path)) {
            std::cerr << "Couldn't find " << config_path.filename().string()
                     << " in " << config_path.parent_path().string() << std::endl;
            return 1;
        }

        // Load shim configuration
        std::string target_path;
        std::string add_args;
        if (!load_shim_config(config_path, target_path, add_args)) {
            std::cerr << "Failed to load shim configuration from " << config_path.string() << std::endl;
            return 1;
        }

        // Build command line
        std::string cmd_args = add_args;
        std::string pass_args = build_args_string(command_line_args);

        if (!pass_args.empty()) {
            if (!cmd_args.empty()) cmd_args += " ";
            cmd_args += pass_args;
        }

        if (!cmd_args.empty()) cmd_args = " " + cmd_args;
        std::string cmd = target_path + cmd_args;

        // Fix when GUI applications want to write to a console
        if (GetConsoleWindow() == nullptr) {
            AttachConsole(ATTACH_PARENT_PROCESS);
        }

        // Try to create process
        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};

        if (!CreateProcessA(nullptr, const_cast<char*>(cmd.c_str()),
                           nullptr, nullptr, TRUE, 0, nullptr, nullptr, &si, &pi)) {

            DWORD error = GetLastError();
            if (error == ERROR_ELEVATION_REQUIRED) {
                // Use ShellExecute for elevation
                return execute_with_elevation(target_path, cmd_args);
            }
            return static_cast<int>(error);
        }

        // Wait for process to complete
        WaitForSingleObject(pi.hProcess, INFINITE_WAIT);

        DWORD exit_code = 0;
        GetExitCodeProcess(pi.hProcess, &exit_code);

        // Close handles
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);

        return static_cast<int>(exit_code);
#else
        // Non-Windows implementation would go here
        return 1;
#endif
    }

    static bool create_shim(const std::string& shim_name,
                           const std::filesystem::path& target_path,
                           const std::vector<std::string>& args = {},
                           bool is_gui = false) {
        auto& config = Config::instance();
        auto shims_dir = config.get_shims_dir();
        
        try {
            // Ensure shims directory exists
            std::filesystem::create_directories(shims_dir);
            
            auto shim_path = shims_dir / (shim_name + ".exe");
            
            output::debug("Creating shim: " + shim_path.string() + " -> " + target_path.string());

            // Create the shim executable
            if (!create_shim_executable(shim_path, target_path, args, is_gui)) {
                output::debug("Failed to create shim executable: " + shim_path.string());
                return false;
            }

            // Create .shim file with metadata
            if (!create_shim_metadata(shim_path, target_path, args, is_gui)) {
                output::debug("Failed to create shim metadata: " + shim_path.string());
                return false;
            }

            output::debug("Shim created successfully: " + shim_name);
            return true;
            
        } catch (const std::exception& e) {
            output::error("Failed to create shim " + shim_name + ": " + e.what());
            return false;
        }
    }
    
    static bool remove_shim(const std::string& shim_name) {
        auto& config = Config::instance();
        auto shims_dir = config.get_shims_dir();
        
        try {
            auto shim_path = shims_dir / (shim_name + ".exe");
            auto metadata_path = shims_dir / (shim_name + ".shim");
            
            bool removed = false;
            
            if (std::filesystem::exists(shim_path)) {
                std::filesystem::remove(shim_path);
                removed = true;
                output::debug("Removed shim executable: " + shim_path.string());
            }

            if (std::filesystem::exists(metadata_path)) {
                std::filesystem::remove(metadata_path);
                output::debug("Removed shim metadata: " + metadata_path.string());
            }

            return removed;

        } catch (const std::exception& e) {
            output::error("Failed to remove shim " + shim_name + ": " + e.what());
            return false;
        }
    }
    
    static std::vector<ShimInfo> list_shims() {
        std::vector<ShimInfo> shims;
        auto& config = Config::instance();
        auto shims_dir = config.get_shims_dir();
        
        if (!std::filesystem::exists(shims_dir)) {
            return shims;
        }
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(shims_dir)) {
                if (entry.is_regular_file() && entry.path().extension() == ".exe") {
                    std::string shim_name = entry.path().stem().string();
                    auto metadata_path = shims_dir / (shim_name + ".shim");
                    
                    ShimInfo info;
                    info.name = shim_name;
                    info.shim_path = entry.path();
                    
                    if (std::filesystem::exists(metadata_path)) {
                        load_shim_metadata(metadata_path, info);
                    }
                    
                    shims.push_back(info);
                }
            }
        } catch (const std::exception& e) {
            output::error("Failed to list shims: " + std::string(e.what()));
        }
        
        return shims;
    }
    
    static bool create_shims_for_app(const std::string& app_name,
                                   const std::filesystem::path& app_dir,
                                   const std::vector<std::string>& bin_entries) {
        bool all_success = true;
        
        for (const auto& bin_entry : bin_entries) {
            if (!create_shim_for_bin_entry(app_name, app_dir, bin_entry)) {
                all_success = false;
            }
        }
        
        return all_success;
    }
    
    static bool remove_shims_for_app(const std::string& app_name) {
        auto shims = list_shims();
        bool all_success = true;

        auto& config = Config::instance();
        auto local_app_dir = config.get_apps_dir() / app_name;
        auto global_app_dir = config.get_global_apps_dir() / app_name;

        for (const auto& shim : shims) {
            // Check if this shim belongs to the app by checking if the target path
            // is within the app's directory (either local or global)
            std::string target_str = shim.target_path.string();
            std::string local_app_str = local_app_dir.string();
            std::string global_app_str = global_app_dir.string();

            bool belongs_to_app = false;

            // Check if target path starts with the app directory path
            if (target_str.size() >= local_app_str.size() &&
                target_str.substr(0, local_app_str.size()) == local_app_str) {
                belongs_to_app = true;
            } else if (target_str.size() >= global_app_str.size() &&
                       target_str.substr(0, global_app_str.size()) == global_app_str) {
                belongs_to_app = true;
            }

            if (belongs_to_app) {
                output::debug("Removing shim '" + shim.name + "' for app '" + app_name + "'");
                if (!remove_shim(shim.name)) {
                    output::debug("Failed to remove shim: " + shim.name);
                    all_success = false;
                }
            }
        }

        return all_success;
    }
    
private:
    // Load shim configuration from .shim file
    static bool load_shim_config(const std::filesystem::path& config_path,
                                std::string& target_path, std::string& args) {
        try {
            std::ifstream config_file(config_path);
            if (!config_file.is_open()) {
                return false;
            }

            std::string line;
            while (std::getline(config_file, line)) {
                size_t eq_pos = line.find('=');
                if (eq_pos == std::string::npos) continue;

                std::string key = line.substr(0, eq_pos);
                std::string value = line.substr(eq_pos + 1);

                if (key == "target") {
                    target_path = value;
                } else if (key == "args") {
                    args = value;
                }
            }

            return !target_path.empty();

        } catch (const std::exception&) {
            return false;
        }
    }

    // Build arguments string from vector
    static std::string build_args_string(const std::vector<std::string>& args) {
        if (args.empty()) return "";

        std::ostringstream oss;
        for (size_t i = 0; i < args.size(); ++i) {
            if (i > 0) oss << " ";

            // Quote arguments that contain spaces
            const std::string& arg = args[i];
            if (arg.find(' ') != std::string::npos) {
                oss << "\"" << arg << "\"";
            } else {
                oss << arg;
            }
        }
        return oss.str();
    }

#ifdef _WIN32
    // Execute with elevation using ShellExecute
    static int execute_with_elevation(const std::string& target_path, const std::string& args) {
        SHELLEXECUTEINFOA sei = {};
        sei.cbSize = sizeof(sei);
        sei.fMask = SEE_MASK_NOCLOSEPROCESS;
        sei.lpVerb = "runas";
        sei.lpFile = target_path.c_str();
        sei.lpParameters = args.empty() ? nullptr : args.c_str();
        sei.nShow = SW_SHOWNORMAL;

        if (!ShellExecuteExA(&sei)) {
            return static_cast<int>(GetLastError());
        }

        if (sei.hProcess) {
            WaitForSingleObject(sei.hProcess, INFINITE_WAIT);

            DWORD exit_code = 0;
            GetExitCodeProcess(sei.hProcess, &exit_code);
            CloseHandle(sei.hProcess);

            return static_cast<int>(exit_code);
        }

        return 0;
    }
#endif

    static bool create_shim_executable(const std::filesystem::path& shim_path,
                                     const std::filesystem::path& target_path,
                                     const std::vector<std::string>& args,
                                     bool is_gui) {
#ifdef _WIN32
        // For now, create a simple batch file wrapper
        // In a full implementation, we would create a proper shim executable
        auto batch_path = shim_path;
        batch_path.replace_extension(".cmd");
        
        std::ofstream batch_file(batch_path);
        if (!batch_file.is_open()) {
            return false;
        }
        
        batch_file << "@echo off\n";
        batch_file << "\"" << target_path.string() << "\"";
        
        for (const auto& arg : args) {
            batch_file << " " << arg;
        }
        
        batch_file << " %*\n";
        batch_file.close();
        
        // Copy the batch file to .exe (Windows will execute .cmd files with .exe extension)
        try {
            std::filesystem::copy_file(batch_path, shim_path);
            std::filesystem::remove(batch_path);
            return true;
        } catch (const std::exception& e) {
            output::error("Failed to create shim executable: " + std::string(e.what()));
            return false;
        }
#else
        // Create a shell script on Unix-like systems
        std::ofstream script_file(shim_path);
        if (!script_file.is_open()) {
            return false;
        }
        
        script_file << "#!/bin/bash\n";
        script_file << "exec \"" << target_path.string() << "\"";
        
        for (const auto& arg : args) {
            script_file << " \"" << arg << "\"";
        }
        
        script_file << " \"$@\"\n";
        script_file.close();
        
        // Make executable
        std::filesystem::permissions(shim_path, 
                                   std::filesystem::perms::owner_exec | 
                                   std::filesystem::perms::group_exec | 
                                   std::filesystem::perms::others_exec,
                                   std::filesystem::perm_options::add);
        
        return true;
#endif
    }
    
    static bool create_shim_metadata(const std::filesystem::path& shim_path,
                                    const std::filesystem::path& target_path,
                                    const std::vector<std::string>& args,
                                    bool is_gui) {
        auto metadata_path = shim_path;
        metadata_path.replace_extension(".shim");
        
        try {
            std::ofstream metadata_file(metadata_path);
            if (!metadata_file.is_open()) {
                return false;
            }
            
            metadata_file << "target=" << target_path.string() << "\n";
            metadata_file << "gui=" << (is_gui ? "true" : "false") << "\n";
            
            if (!args.empty()) {
                metadata_file << "args=";
                for (size_t i = 0; i < args.size(); ++i) {
                    if (i > 0) metadata_file << " ";
                    metadata_file << args[i];
                }
                metadata_file << "\n";
            }
            
            metadata_file.close();
            return true;
            
        } catch (const std::exception& e) {
            output::error("Failed to create shim metadata: " + std::string(e.what()));
            return false;
        }
    }
    
    static void load_shim_metadata(const std::filesystem::path& metadata_path, ShimInfo& info) {
        try {
            std::ifstream metadata_file(metadata_path);
            if (!metadata_file.is_open()) {
                return;
            }
            
            std::string line;
            while (std::getline(metadata_file, line)) {
                size_t eq_pos = line.find('=');
                if (eq_pos == std::string::npos) continue;
                
                std::string key = line.substr(0, eq_pos);
                std::string value = line.substr(eq_pos + 1);
                
                if (key == "target") {
                    info.target_path = value;
                } else if (key == "gui") {
                    info.is_gui = (value == "true");
                } else if (key == "args") {
                    // Parse space-separated arguments
                    std::istringstream iss(value);
                    std::string arg;
                    while (iss >> arg) {
                        info.args.push_back(arg);
                    }
                }
            }
            
        } catch (const std::exception& e) {
            output::error("Failed to load shim metadata: " + std::string(e.what()));
        }
    }
    
    static bool create_shim_for_bin_entry(const std::string& app_name,
                                        const std::filesystem::path& app_dir,
                                        const std::string& bin_entry) {
        // Parse bin entry - can be:
        // - "executable.exe"
        // - ["executable.exe", "alias"]
        // - ["executable.exe", "alias", "arg1", "arg2"]
        
        std::string executable;
        std::string shim_name;
        std::vector<std::string> args;
        
        // For now, assume simple string format
        // TODO: Parse JSON array format
        executable = bin_entry;
        shim_name = std::filesystem::path(executable).stem().string();
        
        auto target_path = app_dir / "current" / executable;
        if (!std::filesystem::exists(target_path)) {
            // Try without "current" subdirectory
            target_path = app_dir / executable;
            if (!std::filesystem::exists(target_path)) {
                output::error("Executable not found: " + target_path.string());
                return false;
            }
        }
        
        return create_shim(shim_name, target_path, args, false);
    }
    
public:
    // Check if shims directory is in PATH
    static bool is_shims_in_path() {
        auto& config = Config::instance();
        auto shims_dir = config.get_shims_dir();
        
#ifdef _WIN32
        char* path_env = nullptr;
        size_t len = 0;
        if (_dupenv_s(&path_env, &len, "PATH") == 0 && path_env != nullptr) {
            std::string path_str(path_env);
            free(path_env);
            
            return path_str.find(shims_dir.string()) != std::string::npos;
        }
#else
        const char* path_env = getenv("PATH");
        if (path_env) {
            std::string path_str(path_env);
            return path_str.find(shims_dir.string()) != std::string::npos;
        }
#endif
        
        return false;
    }
    
    // Add shims directory to PATH (requires restart or manual refresh)
    static bool add_shims_to_path() {
        auto& config = Config::instance();
        auto shims_dir = config.get_shims_dir();
        
        if (is_shims_in_path()) {
            output::debug("Shims directory already in PATH");
            return true;
        }
        
#ifdef _WIN32
        // Add to user PATH environment variable
        HKEY hKey;
        LONG result = RegOpenKeyExA(HKEY_CURRENT_USER, "Environment", 0, KEY_ALL_ACCESS, &hKey);
        if (result != ERROR_SUCCESS) {
            output::error("Failed to open registry key for PATH modification");
            return false;
        }
        
        char current_path[32767];
        DWORD path_size = sizeof(current_path);
        result = RegQueryValueExA(hKey, "PATH", NULL, NULL, (LPBYTE)current_path, &path_size);
        
        std::string new_path;
        if (result == ERROR_SUCCESS) {
            new_path = std::string(current_path) + ";" + shims_dir.string();
        } else {
            new_path = shims_dir.string();
        }
        
        result = RegSetValueExA(hKey, "PATH", 0, REG_EXPAND_SZ, 
                               (LPBYTE)new_path.c_str(), new_path.length() + 1);
        
        RegCloseKey(hKey);
        
        if (result == ERROR_SUCCESS) {
            std::cout << "Added shims directory to PATH: " << shims_dir.string() << "\n";
            std::cout << "Please restart your terminal or refresh environment variables\n";
            return true;
        } else {
            std::cerr << "Failed to update PATH in registry\n";
            return false;
        }
#else
        std::cout << "Automatic PATH modification not implemented for non-Windows platforms\n";
        std::cout << "Please add " << shims_dir.string() << " to your PATH manually\n";
        return false;
#endif
    }
};

} // namespace sco
