#include "src/utils/shim_manager.hpp"
#include <iostream>
#include <filesystem>

int main() {
    using namespace sco;
    
    // Test creating a shim
    std::string shim_name = "test_shim";
    std::filesystem::path target_path = "C:\\Windows\\System32\\notepad.exe";
    std::vector<std::string> args = {};
    
    std::cout << "Creating test shim..." << std::endl;
    
    if (ShimManager::create_shim(shim_name, target_path, args, false)) {
        std::cout << "Shim created successfully!" << std::endl;
        
        // Test executing the shim
        auto& config = Config::instance();
        auto shim_path = config.get_shims_dir() / (shim_name + ".exe");
        
        std::cout << "Testing shim execution..." << std::endl;
        std::vector<std::string> test_args = {"test.txt"};
        
        int result = ShimManager::execute_shim(shim_path, test_args);
        std::cout << "Shim execution result: " << result << std::endl;
        
        // Clean up
        std::cout << "Cleaning up..." << std::endl;
        ShimManager::remove_shim(shim_name);
        
    } else {
        std::cout << "Failed to create shim!" << std::endl;
        return 1;
    }
    
    return 0;
}
